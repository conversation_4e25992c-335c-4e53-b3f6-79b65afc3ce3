#pragma once

#include <windows.h>
#include <iostream>

// DLL Handles and Function Pointers
HINSTANCE hEcrSerialComLib;

void loadTheFunctions();

// Function pointer types for ECRBridge.dll - with proper calling convention
typedef int(__cdecl* SaleFunc)(double amount);
typedef int(__cdecl* RefundFunc)(double amount);
typedef int(__cdecl* PreAuthFunc)(double amount);
typedef int(__cdecl* SettlementFunc)();
typedef int(__cdecl* VoidFunc)(const char* transactionId);
typedef int(__cdecl* DetailReportFunc)();
typedef int(__cdecl* TotalReportFunc)();
typedef int(__cdecl* ReprintFunc)();
typedef const char* (__cdecl* GetResponseTextFunc)();
typedef const char* (__cdecl* GetTransactionIdFunc)();
typedef const char* (__cdecl* GetApprovalCodeFunc)();
typedef int(__cdecl* GetResponseCodeFunc)();

// Connection management function types
typedef int(__cdecl* InitializeConnectionFunc)(const char* portName, int baudRate);
typedef int(__cdecl* CloseConnectionFunc)();
typedef int(__cdecl* IsConnectionActiveFunc)();

// Function pointers
SaleFunc Sale;
RefundFunc Refund;
PreAuthFunc PreAuth;
SettlementFunc Settlement;
VoidFunc VoidTransaction;
DetailReportFunc DetailReport;
TotalReportFunc TotalReport;
ReprintFunc Reprint;
GetResponseTextFunc GetResponseText;
GetTransactionIdFunc GetTransactionIdF;
GetApprovalCodeFunc GetApprovalCode;
GetResponseCodeFunc GetResponseCode;

// Connection management function pointers
InitializeConnectionFunc InitializeConnection;
CloseConnectionFunc CloseConnection;
IsConnectionActiveFunc IsConnectionActive;

////////////////////////////////////////////
//***************************************************************************/
//  FILE:		stdafx.h
//                                                     
//  TITLE:		StandAlonePay Standard Headers                                                   
//                                                     
//  AUTHOR:		Steven Owens (06/04/2025)                                                   
//                                                                              
//***************************************************************************/

#pragma once
#pragma warning( disable: 4251 )

#define WIN32_LEAN_AND_MEAN             // Exclude rarely-used stuff from Windows headers
#define WINDOWS_IGNORE_PACKING_MISMATCH
#define _WINSOCK_DEPRECATED_NO_WARNINGS

#if _MSC_VER >= 1500					// VS2008
#include <atlstr.h>					//CString
#else
#include <afx.h>					//CString for VC6, Also may need Project / Settings / Use MFC in a shared DLL
#endif

// Windows Header Files
#include <windows.h>
#include <string>
#include <sstream>
#include <vector>
#include <algorithm>

///////////////////////////////////////////