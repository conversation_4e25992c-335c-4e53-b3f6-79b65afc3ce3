// TestTheDll.cpp : This file contains the 'main' function. Program execution begins and ends there.
//

#include "TestTheDll.h"

int main()
{
    std::cout << "Hello World!\n";
    loadTheFunctions();

    // Initialize the connection
    std::string portName = "COM17";
    int baud = 115200;

    int hello = IsConnectionActive();

    if (IsConnectionActive() != 1)
    {
        CloseConnection();
        int connectionResult = InitializeConnection(portName.c_str(), baud);
        if (connectionResult != 1)
        {
            std::cout << "Not loaded";
        }
    }
}

// Run program: Ctrl + F5 or Debug > Start Without Debugging menu
// Debug program: F5 or Debug > Start Debugging menu

// Tips for Getting Started: 
//   1. Use the Solution Explorer window to add/manage files
//   2. Use the Team Explorer window to connect to source control
//   3. Use the Output window to see build output and other messages
//   4. Use the Error List window to view errors
//   5. Go to Project > Add New Item to create new code files, or Project > Add Existing Item to add existing code files to the project
//   6. In the future, to open this project again, go to File > Open > Project and select the .sln file

void loadTheFunctions()
{
    // Build full path to DLL
    std::string fullDllPath = "C:\\Users\\<USER>\\source\\repos\\TestTheDll\\ECRBridge.dll";
    hEcrSerialComLib = LoadLibraryA(fullDllPath.c_str());

    if (!hEcrSerialComLib) {
        DWORD errorCode = GetLastError();
        std::cerr << "LoadLibrary failed with error: " << errorCode << std::endl;
    }

    // Get function pointers
    Sale = (SaleFunc)GetProcAddress(hEcrSerialComLib, "Sale");
    Refund = (RefundFunc)GetProcAddress(hEcrSerialComLib, "Refund");
    PreAuth = (PreAuthFunc)GetProcAddress(hEcrSerialComLib, "PreAuth");
    Settlement = (SettlementFunc)GetProcAddress(hEcrSerialComLib, "Settlement");
    VoidTransaction = (VoidFunc)GetProcAddress(hEcrSerialComLib, "VoidTransaction");
    DetailReport = (DetailReportFunc)GetProcAddress(hEcrSerialComLib, "DetailReport");
    TotalReport = (TotalReportFunc)GetProcAddress(hEcrSerialComLib, "TotalReport");
    Reprint = (ReprintFunc)GetProcAddress(hEcrSerialComLib, "Reprint");
    GetResponseText = (GetResponseTextFunc)GetProcAddress(hEcrSerialComLib, "GetResponseText");
    GetTransactionIdF = (GetTransactionIdFunc)GetProcAddress(hEcrSerialComLib, "GetTransactionId");
    GetApprovalCode = (GetApprovalCodeFunc)GetProcAddress(hEcrSerialComLib, "GetApprovalCode");
    GetResponseCode = (GetResponseCodeFunc)GetProcAddress(hEcrSerialComLib, "GetResponseCode");

    // Connection management functions
    InitializeConnection = (InitializeConnectionFunc)GetProcAddress(hEcrSerialComLib, "InitializeConnection");
    CloseConnection = (CloseConnectionFunc)GetProcAddress(hEcrSerialComLib, "CloseConnection");
    IsConnectionActive = (IsConnectionActiveFunc)GetProcAddress(hEcrSerialComLib, "IsConnectionActive");
}